from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
import asyncio
import json
import base64
import io
from datetime import datetime
from typing import Dict, Any, List
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt

from app.backend.models.schemas import ErrorResponse, BacktestRequest
from src.backtester import Backtester
from src.main import run_hedge_fund

router = APIRouter(prefix="/backtest")


@router.post(
    path="/run",
    responses={
        200: {"description": "Successful response with streaming backtest updates"},
        400: {"model": ErrorResponse, "description": "Invalid request parameters"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
)
async def run_backtest(request: BacktestRequest):
    """Run a backtest with the specified parameters."""
    try:
        # Validate request parameters
        if not request.tickers:
            raise HTTPException(status_code=400, detail="At least one ticker must be provided")
        
        if not request.selected_analysts:
            raise HTTPException(status_code=400, detail="At least one analyst must be selected")
        
        # Validate date format and range
        try:
            start_date = datetime.strptime(request.start_date, "%Y-%m-%d")
            end_date = datetime.strptime(request.end_date, "%Y-%m-%d")
            
            if start_date >= end_date:
                raise HTTPException(status_code=400, detail="Start date must be before end date")
                
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")

        # Convert model_provider to string if it's an enum
        model_provider = request.model_provider
        if hasattr(model_provider, "value"):
            model_provider = model_provider.value

        # Create a thread-safe way to capture backtest results and progress
        result_container = {"performance_metrics": None, "error": None}
        progress_queue = []

        def progress_callback(progress_data):
            progress_queue.append(progress_data)

        async def event_generator():
            try:
                # Send start event
                start_event = {
                    "type": "start",
                    "data": {
                        "message": "Starting backtest...",
                        "tickers": request.tickers,
                        "start_date": request.start_date,
                        "end_date": request.end_date,
                        "initial_capital": request.initial_capital,
                        "analysts": request.selected_analysts,
                        "model": request.model_name,
                        "provider": model_provider
                    }
                }
                yield f"data: {json.dumps(start_event)}\n\n"
                await asyncio.sleep(0.1)

                # Create backtester instance
                backtester = Backtester(
                    agent=run_hedge_fund,
                    tickers=request.tickers,
                    start_date=request.start_date,
                    end_date=request.end_date,
                    initial_capital=request.initial_capital,
                    model_name=request.model_name,
                    model_provider=model_provider,
                    selected_analysts=request.selected_analysts,
                    initial_margin_requirement=request.margin_requirement,
                    progress_callback=progress_callback,
                )

                # Send progress update
                progress_event = {
                    "type": "progress",
                    "data": {
                        "message": "Initializing backtest environment...",
                        "step": "initialization"
                    }
                }
                yield f"data: {json.dumps(progress_event)}\n\n"
                await asyncio.sleep(0.1)

                # Run the backtest in a separate thread to avoid blocking
                import concurrent.futures
                import threading

                def run_backtest_sync():
                    try:
                        performance_metrics = backtester.run_backtest()
                        result_container["performance_metrics"] = performance_metrics
                    except Exception as e:
                        result_container["error"] = str(e)

                # Run backtest in thread
                thread = threading.Thread(target=run_backtest_sync)
                thread.start()
                
                # Monitor progress while backtest runs
                step_count = 0
                last_progress_count = 0
                while thread.is_alive():
                    step_count += 1

                    # Check for new detailed progress updates
                    if len(progress_queue) > last_progress_count:
                        for i in range(last_progress_count, len(progress_queue)):
                            detailed_progress = progress_queue[i]
                            progress_event = {
                                "type": "detailed_progress",
                                "data": detailed_progress
                            }
                            yield f"data: {json.dumps(progress_event)}\n\n"
                        last_progress_count = len(progress_queue)
                    else:
                        # Send generic progress if no detailed updates
                        progress_event = {
                            "type": "progress",
                            "data": {
                                "message": f"Running backtest... Step {step_count}",
                                "step": f"backtest_step_{step_count}"
                            }
                        }
                        yield f"data: {json.dumps(progress_event)}\n\n"

                    await asyncio.sleep(2)  # Update every 2 seconds
                
                # Wait for thread to complete
                thread.join()
                
                # Check for errors
                if result_container["error"]:
                    error_event = {
                        "type": "error",
                        "data": {
                            "message": f"Backtest failed: {result_container['error']}"
                        }
                    }
                    yield f"data: {json.dumps(error_event)}\n\n"
                    return

                # Send completion event with results
                performance_metrics = result_container["performance_metrics"]
                
                # Get performance analysis and chart data
                performance_df, chart_data = backtester.analyze_performance()
                
                # Prepare results summary
                results_summary = {
                    "total_return": None,
                    "sharpe_ratio": None,
                    "max_drawdown": None,
                    "final_portfolio_value": backtester.portfolio["cash"] + sum(
                        backtester.portfolio["positions"].get(ticker, 0) * 
                        backtester.get_current_price(ticker, request.end_date)
                        for ticker in request.tickers
                    ) if hasattr(backtester, 'get_current_price') else None
                }
                
                if performance_metrics:
                    results_summary.update({
                        "sharpe_ratio": performance_metrics.get("sharpe_ratio"),
                        "max_drawdown": performance_metrics.get("max_drawdown"),
                        "sortino_ratio": performance_metrics.get("sortino_ratio"),
                    })
                
                if not performance_df.empty and len(performance_df) > 0:
                    initial_value = performance_df.iloc[0]["Portfolio Value"]
                    final_value = performance_df.iloc[-1]["Portfolio Value"]
                    total_return = ((final_value - initial_value) / initial_value) * 100
                    results_summary["total_return"] = total_return
                    results_summary["final_portfolio_value"] = final_value

                complete_event = {
                    "type": "complete",
                    "data": {
                        "message": "Backtest completed successfully!",
                        "results": results_summary,
                        "performance_metrics": performance_metrics,
                        "chart_data": chart_data,
                        "backtest_summary": {
                            "start_date": request.start_date,
                            "end_date": request.end_date,
                            "initial_capital": request.initial_capital,
                            "tickers": request.tickers,
                            "analysts_used": request.selected_analysts,
                            "model_used": f"{model_provider}/{request.model_name}"
                        }
                    }
                }
                yield f"data: {json.dumps(complete_event)}\n\n"

            except Exception as e:
                error_event = {
                    "type": "error",
                    "data": {
                        "message": f"Backtest error: {str(e)}"
                    }
                }
                yield f"data: {json.dumps(error_event)}\n\n"

        return StreamingResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/status")
async def get_backtest_status():
    """Get the current status of backtest functionality."""
    return {
        "success": True,
        "data": {
            "available": True,
            "message": "Backtest functionality is available"
        }
    }
