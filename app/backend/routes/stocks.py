from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime, timedelta

from app.backend.services.stock_service import get_stock_service
from app.backend.services.financial_service import get_financial_service, FinancialService
from app.backend.providers.factory import get_available_providers

router = APIRouter(prefix="/stocks")


class TickerValidationRequest(BaseModel):
    tickers: List[str]


@router.get("/search")
async def search_stocks(q: str = Query(..., min_length=1, description="Search query")):
    """Search for stocks by ticker or company name."""
    try:
        stock_service = get_stock_service()
        results = await stock_service.search_stocks(q, limit=10)

        # Convert to dict format for API response
        results_data = [
            {
                "ticker": result.ticker,
                "name": result.name,
                "exchange": result.exchange,
                "sector": result.sector,
                "industry": result.industry,
            }
            for result in results
        ]

        return {"success": True, "data": results_data}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/{ticker}/facts")
async def get_stock_facts(ticker: str):
    """Get company facts for a specific ticker."""
    try:
        stock_service = get_stock_service()
        company_facts = await stock_service.get_company_facts(ticker)

        if not company_facts:
            raise HTTPException(status_code=404, detail=f"Company facts not found for {ticker}")

        return {
            "success": True,
            "data": {
                "ticker": company_facts.ticker,
                "name": company_facts.name,
                "exchange": company_facts.exchange,
                "sector": company_facts.sector,
                "industry": company_facts.industry,
                "marketCap": company_facts.market_cap,
                "numberOfEmployees": company_facts.number_of_employees,
                "websiteUrl": company_facts.website_url,
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch company facts: {str(e)}")


@router.post("/validate")
async def validate_tickers(request: TickerValidationRequest):
    """Validate a list of ticker symbols."""
    try:
        stock_service = get_stock_service()
        validation_result = await stock_service.validate_tickers(request.tickers)

        return {
            "success": True,
            "data": {
                "valid": validation_result.valid,
                "invalid": validation_result.invalid,
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")


@router.get("/provider/info")
async def get_provider_info():
    """Get information about the current stock data provider."""
    try:
        stock_service = get_stock_service()
        provider_info = stock_service.get_provider_info()

        return {
            "success": True,
            "data": provider_info
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get provider info: {str(e)}")


@router.get("/providers")
async def get_data_providers():
    """Get list of available data providers."""
    try:
        providers = get_available_providers()

        return {
            "success": True,
            "data": [
                {
                    "id": provider_id,
                    "name": provider_id.replace("_", " ").title(),
                    "description": description
                }
                for provider_id, description in providers.items()
            ]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get providers: {str(e)}")


@router.get("/{ticker}/news")
async def get_company_news(
    ticker: str,
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)"),
    limit: int = Query(50, description="Maximum number of news items to return"),
    provider: Optional[str] = Query(None, description="Data provider to use")
):
    """Get company news for a specific ticker."""
    try:
        # Use default end_date as today if not provided
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")

        # Use default start_date as 30 days ago if not provided
        if not start_date:
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")

        # Get financial service (optionally with specific provider)
        if provider:
            from app.backend.providers.factory import get_financial_provider
            provider_instance = get_financial_provider(provider)
            financial_service = FinancialService(provider_instance)
        else:
            financial_service = get_financial_service()

        # Fetch news
        news_items = await financial_service.get_company_news(
            ticker=ticker,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )

        # Convert to API response format
        news_data = [
            {
                "ticker": news.ticker,
                "title": news.title,
                "author": news.author,
                "source": news.source,
                "date": news.date,
                "url": news.url,
                "sentiment": news.sentiment,
            }
            for news in news_items
        ]

        return {
            "success": True,
            "data": news_data,
            "meta": {
                "ticker": ticker,
                "start_date": start_date,
                "end_date": end_date,
                "limit": limit,
                "count": len(news_data)
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch news for {ticker}: {str(e)}")
