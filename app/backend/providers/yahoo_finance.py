"""Yahoo Finance provider for comprehensive financial data."""

import asyncio
import pandas as pd
from typing import List, Optional
from datetime import datetime, timedelta
from .financial_base import FinancialDataProvider, prices_to_df
from .base import StockSearchResult, CompanyFacts, TickerValidationResult
from src.data.models import (
    Price,
    FinancialMetrics,
    LineItem,
    InsiderTrade,
    CompanyNews,
)

try:
    import yfinance as yf
except ImportError:
    yf = None


class YahooFinanceProvider(FinancialDataProvider):
    """Comprehensive financial data provider using Yahoo Finance API via yfinance.

    Note: Yahoo Finance has limitations compared to Financial Datasets:
    - Limited historical financial metrics
    - No insider trading data
    - No company news API
    - Limited line items search
    """
    
    def __init__(self, timeout: int = 30):
        """Initialize the Yahoo Finance provider.
        
        Args:
            timeout: Request timeout in seconds
        """
        super().__init__(timeout)
        if yf is None:
            raise ImportError("yfinance package is required for Yahoo Finance provider. Install with: pip install yfinance")
    
    async def search_stocks(self, query: str, limit: int = 10) -> List[StockSearchResult]:
        """Search for stocks by ticker or company name.
        
        Note: Yahoo Finance doesn't have a direct search API, so we'll try to validate
        the query as a ticker and return it if valid, or return popular matches.
        """
        query_upper = query.upper().strip()
        results = []
        
        # Try to get info for the query as a ticker
        try:
            ticker_obj = yf.Ticker(query_upper)
            info = await asyncio.get_event_loop().run_in_executor(
                None, lambda: ticker_obj.info
            )
            
            if info and info.get('symbol'):
                result = StockSearchResult(
                    ticker=info.get('symbol', query_upper),
                    name=info.get('longName', info.get('shortName', '')),
                    exchange=info.get('exchange', ''),
                    sector=info.get('sector', ''),
                    industry=info.get('industry', '')
                )
                results.append(result)
        except Exception:
            pass
        
        # If no results and query looks like a partial ticker, try some common variations
        if not results and len(query) <= 5:
            common_tickers = [
                "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", 
                "JPM", "JNJ", "V", "PG", "UNH", "HD", "MA", "BAC"
            ]
            
            for ticker in common_tickers:
                if query.upper() in ticker:
                    try:
                        ticker_obj = yf.Ticker(ticker)
                        info = await asyncio.get_event_loop().run_in_executor(
                            None, lambda t=ticker: yf.Ticker(t).info
                        )
                        
                        if info and info.get('symbol'):
                            result = StockSearchResult(
                                ticker=info.get('symbol', ticker),
                                name=info.get('longName', info.get('shortName', '')),
                                exchange=info.get('exchange', ''),
                                sector=info.get('sector', ''),
                                industry=info.get('industry', '')
                            )
                            results.append(result)
                            
                            if len(results) >= limit:
                                break
                    except Exception:
                        continue
        
        return results[:limit]
    
    async def get_company_facts(self, ticker: str) -> Optional[CompanyFacts]:
        """Get company facts for a specific ticker."""
        try:
            ticker_obj = yf.Ticker(ticker.upper())
            info = await asyncio.get_event_loop().run_in_executor(
                None, lambda: ticker_obj.info
            )
            
            if not info or not info.get('symbol'):
                return None
            
            # Extract relevant information
            market_cap = info.get('marketCap')
            if market_cap is None:
                # Try to calculate from shares outstanding and current price
                shares = info.get('sharesOutstanding')
                price = info.get('currentPrice') or info.get('regularMarketPrice')
                if shares and price:
                    market_cap = shares * price
            
            return CompanyFacts(
                ticker=info.get('symbol', ticker.upper()),
                name=info.get('longName', info.get('shortName', '')),
                exchange=info.get('exchange', ''),
                sector=info.get('sector', ''),
                industry=info.get('industry', ''),
                market_cap=market_cap,
                number_of_employees=info.get('fullTimeEmployees'),
                website_url=info.get('website', '')
            )
            
        except Exception:
            return None
    
    async def validate_tickers(self, tickers: List[str]) -> TickerValidationResult:
        """Validate a list of ticker symbols."""
        valid_tickers = []
        invalid_tickers = []
        
        async def validate_single_ticker(ticker: str) -> bool:
            """Validate a single ticker."""
            try:
                ticker_obj = yf.Ticker(ticker.upper())
                info = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: ticker_obj.info
                )
                return bool(info and info.get('symbol'))
            except Exception:
                return False
        
        # Validate tickers concurrently for better performance
        tasks = [validate_single_ticker(ticker) for ticker in tickers]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for ticker, result in zip(tickers, results):
            if isinstance(result, bool) and result:
                valid_tickers.append(ticker.upper())
            else:
                invalid_tickers.append(ticker.upper())
        
        return TickerValidationResult(valid=valid_tickers, invalid=invalid_tickers)
    
    # Price data methods
    async def get_prices(self, ticker: str, start_date: str, end_date: str) -> List[Price]:
        """Fetch price data for a ticker within date range."""
        try:
            ticker_obj = yf.Ticker(ticker.upper())
            hist = await asyncio.get_event_loop().run_in_executor(
                None, lambda: ticker_obj.history(start=start_date, end=end_date)
            )

            if hist.empty:
                return []

            prices = []
            for date, row in hist.iterrows():
                price = Price(
                    time=date.strftime('%Y-%m-%d'),
                    open=float(row['Open']),
                    high=float(row['High']),
                    low=float(row['Low']),
                    close=float(row['Close']),
                    volume=int(row['Volume']) if pd.notna(row['Volume']) else 0
                )
                prices.append(price)

            return prices

        except Exception as e:
            print(f"Error fetching prices for {ticker}: {e}")
            return []

    async def get_price_data(self, ticker: str, start_date: str, end_date: str) -> pd.DataFrame:
        """Fetch price data as DataFrame for a ticker within date range."""
        try:
            ticker_obj = yf.Ticker(ticker.upper())
            hist = await asyncio.get_event_loop().run_in_executor(
                None, lambda: ticker_obj.history(start=start_date, end=end_date)
            )

            if hist.empty:
                return pd.DataFrame()

            # Rename columns to match expected format
            hist = hist.rename(columns={
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume'
            })

            # Add time column
            hist['time'] = hist.index.strftime('%Y-%m-%d')
            hist.index.name = 'Date'

            return hist

        except Exception as e:
            print(f"Error fetching price data for {ticker}: {e}")
            return pd.DataFrame()

    # Financial metrics methods
    async def get_financial_metrics(
        self,
        ticker: str,
        end_date: str,
        period: str = "ttm",
        limit: int = 10
    ) -> List[FinancialMetrics]:
        """Fetch financial metrics for a ticker.

        Note: Yahoo Finance has limited financial metrics compared to Financial Datasets.
        This returns basic metrics from the info endpoint.
        """
        try:
            ticker_obj = yf.Ticker(ticker.upper())
            info = await asyncio.get_event_loop().run_in_executor(
                None, lambda: ticker_obj.info
            )

            if not info:
                return []

            # Create a basic financial metrics object from available info
            # Set default values for required fields that Yahoo Finance doesn't provide
            metrics = FinancialMetrics(
                ticker=ticker.upper(),
                report_period=end_date,
                period=period,
                currency=info.get('currency', 'USD'),  # Default to USD
                market_cap=info.get('marketCap'),
                # Set all required fields with defaults
                enterprise_value=info.get('enterpriseValue'),
                price_to_earnings_ratio=info.get('trailingPE'),
                price_to_book_ratio=info.get('priceToBook'),
                price_to_sales_ratio=info.get('priceToSalesTrailing12Months'),
                enterprise_value_to_ebitda_ratio=info.get('enterpriseToEbitda'),
                enterprise_value_to_revenue_ratio=info.get('enterpriseToRevenue'),
                free_cash_flow_yield=None,
                peg_ratio=info.get('pegRatio'),
                gross_margin=info.get('grossMargins'),
                operating_margin=info.get('operatingMargins'),
                net_margin=info.get('profitMargins'),
                return_on_equity=info.get('returnOnEquity'),
                return_on_assets=info.get('returnOnAssets'),
                return_on_invested_capital=None,
                asset_turnover=None,
                inventory_turnover=None,
                receivables_turnover=None,
                days_sales_outstanding=None,
                operating_cycle=None,
                working_capital_turnover=None,
                current_ratio=info.get('currentRatio'),
                quick_ratio=info.get('quickRatio'),
                cash_ratio=None,
                operating_cash_flow_ratio=None,
                debt_to_equity=info.get('debtToEquity'),
                debt_to_assets=None,
                interest_coverage=None,
                revenue_growth=info.get('revenueGrowth'),
                earnings_growth=info.get('earningsGrowth'),
                book_value_growth=None,
                earnings_per_share_growth=None,
                free_cash_flow_growth=None,
                operating_income_growth=None,
                ebitda_growth=None,
                payout_ratio=info.get('payoutRatio'),
                earnings_per_share=info.get('trailingEps'),
                book_value_per_share=info.get('bookValue'),
                free_cash_flow_per_share=None,
            )

            return [metrics]

        except Exception as e:
            print(f"Error fetching financial metrics for {ticker}: {e}")
            return []

    async def search_line_items(
        self,
        ticker: str,
        line_items: List[str],
        end_date: str,
        period: str = "ttm",
        limit: int = 10,
    ) -> List[LineItem]:
        """Search for specific line items in financial statements.

        Note: Yahoo Finance doesn't support line item search.
        This method returns empty results.
        """
        print(f"Warning: Line item search not supported by Yahoo Finance provider for {ticker}")
        return []

    # Market data methods
    async def get_market_cap(self, ticker: str, end_date: str) -> Optional[float]:
        """Get market capitalization for a ticker."""
        try:
            ticker_obj = yf.Ticker(ticker.upper())
            info = await asyncio.get_event_loop().run_in_executor(
                None, lambda: ticker_obj.info
            )

            if not info:
                return None

            market_cap = info.get('marketCap')
            if market_cap is None:
                # Try to calculate from shares outstanding and current price
                shares = info.get('sharesOutstanding')
                price = info.get('currentPrice') or info.get('regularMarketPrice')
                if shares and price:
                    market_cap = shares * price

            return market_cap

        except Exception as e:
            print(f"Error fetching market cap for {ticker}: {e}")
            return None

    # News and insider trading methods
    async def get_company_news(
        self,
        ticker: str,
        end_date: str,
        start_date: Optional[str] = None,
        limit: int = 1000,
    ) -> List[CompanyNews]:
        """Fetch company news for a ticker within date range."""
        try:
            import yfinance as yf
            from datetime import datetime, timezone

            # Get the ticker object
            ticker_obj = yf.Ticker(ticker)

            # Fetch news
            news_data = ticker_obj.news

            if not news_data:
                return []

            # Convert to our CompanyNews format
            company_news = []

            for item in news_data[:limit]:  # Limit the results
                try:
                    content = item.get('content', {})

                    # Extract basic information
                    title = content.get('title', 'No title')
                    pub_date = content.get('pubDate', '')

                    # Parse date
                    if pub_date:
                        try:
                            # Parse ISO format date
                            date_obj = datetime.fromisoformat(pub_date.replace('Z', '+00:00'))
                            formatted_date = date_obj.strftime('%Y-%m-%d')
                        except:
                            formatted_date = pub_date[:10] if len(pub_date) >= 10 else pub_date
                    else:
                        formatted_date = datetime.now().strftime('%Y-%m-%d')

                    # Filter by date range if specified
                    if start_date and formatted_date < start_date:
                        continue
                    if end_date and formatted_date > end_date:
                        continue

                    # Extract other fields
                    provider = content.get('provider', {})
                    source = provider.get('displayName', 'Yahoo Finance')

                    # Get URL
                    canonical_url = content.get('canonicalUrl', {})
                    url = canonical_url.get('url', '') if canonical_url else ''

                    # Create CompanyNews object
                    news_item = CompanyNews(
                        ticker=ticker,
                        title=title,
                        author=source,  # Yahoo Finance doesn't provide author info
                        source=source,
                        date=formatted_date,
                        url=url,
                        sentiment=None  # Yahoo Finance doesn't provide sentiment
                    )

                    company_news.append(news_item)

                except Exception as e:
                    print(f"Error processing news item for {ticker}: {e}")
                    continue

            return company_news

        except Exception as e:
            print(f"Error fetching news for {ticker}: {e}")
            return []

    async def get_insider_trades(
        self,
        ticker: str,
        end_date: str,
        start_date: Optional[str] = None,
        limit: int = 1000,
    ) -> List[InsiderTrade]:
        """Fetch insider trades for a ticker within date range.

        Note: Yahoo Finance doesn't provide insider trading data through yfinance.
        This method returns empty results.
        """
        print(f"Warning: Insider trades not supported by Yahoo Finance provider for {ticker}")
        return []

    @property
    def provider_name(self) -> str:
        """Get the provider name."""
        return "yahoo_finance"
