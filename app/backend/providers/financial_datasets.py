"""Financial Datasets API provider for comprehensive financial data."""

import os
import requests
import pandas as pd
from typing import List, Optional
from .financial_base import FinancialDataProvider, prices_to_df
from .base import StockSearchResult, CompanyFacts, TickerValidationResult
from src.data.models import (
    CompanyFactsResponse,
    Price,
    PriceResponse,
    FinancialMetrics,
    FinancialMetricsResponse,
    LineItem,
    LineItemResponse,
    InsiderTrade,
    InsiderTradeResponse,
    CompanyNews,
    CompanyNewsResponse,
)


class FinancialDatasetsProvider(FinancialDataProvider):
    """Comprehensive financial data provider using Financial Datasets API."""
    
    # Popular stocks for quick search
    POPULAR_STOCKS = [
        {"ticker": "AAPL", "name": "Apple Inc.", "exchange": "NASDAQ", "sector": "Technology"},
        {"ticker": "MSFT", "name": "Microsoft Corporation", "exchange": "NASDAQ", "sector": "Technology"},
        {"ticker": "GOOGL", "name": "Alphabet Inc.", "exchange": "NASDAQ", "sector": "Technology"},
        {"ticker": "AMZN", "name": "Amazon.com Inc.", "exchange": "NASDAQ", "sector": "Consumer Discretionary"},
        {"ticker": "TSLA", "name": "Tesla Inc.", "exchange": "NASDAQ", "sector": "Consumer Discretionary"},
        {"ticker": "META", "name": "Meta Platforms Inc.", "exchange": "NASDAQ", "sector": "Technology"},
        {"ticker": "NVDA", "name": "NVIDIA Corporation", "exchange": "NASDAQ", "sector": "Technology"},
        {"ticker": "JPM", "name": "JPMorgan Chase & Co.", "exchange": "NYSE", "sector": "Financial Services"},
        {"ticker": "JNJ", "name": "Johnson & Johnson", "exchange": "NYSE", "sector": "Healthcare"},
        {"ticker": "V", "name": "Visa Inc.", "exchange": "NYSE", "sector": "Financial Services"},
        {"ticker": "PG", "name": "Procter & Gamble Co.", "exchange": "NYSE", "sector": "Consumer Staples"},
        {"ticker": "UNH", "name": "UnitedHealth Group Inc.", "exchange": "NYSE", "sector": "Healthcare"},
        {"ticker": "HD", "name": "Home Depot Inc.", "exchange": "NYSE", "sector": "Consumer Discretionary"},
        {"ticker": "MA", "name": "Mastercard Inc.", "exchange": "NYSE", "sector": "Financial Services"},
        {"ticker": "BAC", "name": "Bank of America Corp.", "exchange": "NYSE", "sector": "Financial Services"},
        {"ticker": "XOM", "name": "Exxon Mobil Corporation", "exchange": "NYSE", "sector": "Energy"},
        {"ticker": "WMT", "name": "Walmart Inc.", "exchange": "NYSE", "sector": "Consumer Staples"},
        {"ticker": "LLY", "name": "Eli Lilly and Company", "exchange": "NYSE", "sector": "Healthcare"},
        {"ticker": "CVX", "name": "Chevron Corporation", "exchange": "NYSE", "sector": "Energy"},
        {"ticker": "ABBV", "name": "AbbVie Inc.", "exchange": "NYSE", "sector": "Healthcare"},
    ]
    
    def __init__(self, timeout: int = 30):
        """Initialize the Financial Datasets provider.
        
        Args:
            timeout: Request timeout in seconds
        """
        super().__init__(timeout)
        self.api_key = os.environ.get("FINANCIAL_DATASETS_API_KEY")
        self.base_url = "https://api.financialdatasets.ai"
    
    def _get_headers(self) -> dict:
        """Get request headers with API key if available."""
        headers = {}
        if self.api_key:
            headers["X-API-KEY"] = self.api_key
        return headers
    
    async def search_stocks(self, query: str, limit: int = 10) -> List[StockSearchResult]:
        """Search for stocks by ticker or company name."""
        query_lower = query.lower().strip()
        results = []
        
        # Search in popular stocks first
        for stock in self.POPULAR_STOCKS:
            if (query_lower in stock["ticker"].lower() or 
                query_lower in stock["name"].lower()):
                results.append(StockSearchResult(**stock))
        
        # Return limited results
        return results[:limit]
    
    async def get_company_facts(self, ticker: str) -> Optional[CompanyFacts]:
        """Get company facts for a specific ticker."""
        try:
            url = f"{self.base_url}/company/facts/?ticker={ticker.upper()}"
            response = requests.get(url, headers=self._get_headers(), timeout=self.timeout)
            
            if response.status_code != 200:
                return None
            
            data = response.json()
            response_model = CompanyFactsResponse(**data)
            company_facts = response_model.company_facts
            
            return CompanyFacts(
                ticker=company_facts.ticker,
                name=company_facts.name,
                exchange=company_facts.exchange,
                sector=company_facts.sector,
                industry=company_facts.industry,
                market_cap=company_facts.market_cap,
                number_of_employees=company_facts.number_of_employees,
                website_url=company_facts.website_url,
            )
            
        except Exception:
            return None
    
    async def validate_tickers(self, tickers: List[str]) -> TickerValidationResult:
        """Validate a list of ticker symbols."""
        valid_tickers = []
        invalid_tickers = []
        
        for ticker in tickers:
            try:
                url = f"{self.base_url}/company/facts/?ticker={ticker.upper()}"
                response = requests.get(url, headers=self._get_headers(), timeout=5)
                
                if response.status_code == 200:
                    valid_tickers.append(ticker.upper())
                else:
                    invalid_tickers.append(ticker.upper())
                    
            except Exception:
                invalid_tickers.append(ticker.upper())
        
        return TickerValidationResult(valid=valid_tickers, invalid=invalid_tickers)
    
    # Price data methods
    async def get_prices(self, ticker: str, start_date: str, end_date: str) -> List[Price]:
        """Fetch price data for a ticker within date range."""
        try:
            url = f"{self.base_url}/prices/?ticker={ticker}&interval=day&interval_multiplier=1&start_date={start_date}&end_date={end_date}"
            response = requests.get(url, headers=self._get_headers(), timeout=self.timeout)

            if response.status_code != 200:
                raise Exception(f"Error fetching price data: {ticker} - {response.status_code} - {response.text}")

            price_response = PriceResponse(**response.json())
            return price_response.prices or []

        except Exception as e:
            print(f"Error fetching prices for {ticker}: {e}")
            return []

    async def get_price_data(self, ticker: str, start_date: str, end_date: str) -> pd.DataFrame:
        """Fetch price data as DataFrame for a ticker within date range."""
        prices = await self.get_prices(ticker, start_date, end_date)
        return prices_to_df(prices)

    # Financial metrics methods
    async def get_financial_metrics(
        self,
        ticker: str,
        end_date: str,
        period: str = "ttm",
        limit: int = 10
    ) -> List[FinancialMetrics]:
        """Fetch financial metrics for a ticker."""
        try:
            url = f"{self.base_url}/financial-metrics/?ticker={ticker}&report_period_lte={end_date}&limit={limit}&period={period}"
            response = requests.get(url, headers=self._get_headers(), timeout=self.timeout)

            if response.status_code != 200:
                raise Exception(f"Error fetching financial metrics: {ticker} - {response.status_code} - {response.text}")

            metrics_response = FinancialMetricsResponse(**response.json())
            return metrics_response.financial_metrics or []

        except Exception as e:
            print(f"Error fetching financial metrics for {ticker}: {e}")
            return []

    async def search_line_items(
        self,
        ticker: str,
        line_items: List[str],
        end_date: str,
        period: str = "ttm",
        limit: int = 10,
    ) -> List[LineItem]:
        """Search for specific line items in financial statements."""
        try:
            url = f"{self.base_url}/financials/search/line-items"
            body = {
                "tickers": [ticker],
                "line_items": line_items,
                "end_date": end_date,
                "period": period,
                "limit": limit,
            }
            response = requests.post(url, headers=self._get_headers(), json=body, timeout=self.timeout)

            if response.status_code != 200:
                raise Exception(f"Error searching line items: {ticker} - {response.status_code} - {response.text}")

            line_item_response = LineItemResponse(**response.json())
            return line_item_response.search_results or []

        except Exception as e:
            print(f"Error searching line items for {ticker}: {e}")
            return []

    # Market data methods
    async def get_market_cap(self, ticker: str, end_date: str) -> Optional[float]:
        """Get market capitalization for a ticker."""
        try:
            # Try to get from company facts first if end_date is today
            import datetime
            if end_date == datetime.datetime.now().strftime("%Y-%m-%d"):
                company_facts = await self.get_company_facts(ticker)
                if company_facts and company_facts.market_cap:
                    return company_facts.market_cap

            # Fallback to financial metrics
            financial_metrics = await self.get_financial_metrics(ticker, end_date)
            if financial_metrics and financial_metrics[0].market_cap:
                return financial_metrics[0].market_cap

            return None

        except Exception as e:
            print(f"Error fetching market cap for {ticker}: {e}")
            return None

    # News and insider trading methods
    async def get_company_news(
        self,
        ticker: str,
        end_date: str,
        start_date: Optional[str] = None,
        limit: int = 1000,
    ) -> List[CompanyNews]:
        """Fetch company news for a ticker within date range."""
        try:
            all_news = []
            current_end_date = end_date

            while True:
                url = f"{self.base_url}/news/?ticker={ticker}&end_date={current_end_date}"
                if start_date:
                    url += f"&start_date={start_date}"
                url += f"&limit={limit}"

                response = requests.get(url, headers=self._get_headers(), timeout=self.timeout)

                if response.status_code != 200:
                    print(f"Financial Datasets API error {response.status_code}: {response.text}")
                    break

                response_data = response.json()
                news_response = CompanyNewsResponse(**response_data)
                company_news = news_response.news or []

                if not company_news:
                    break

                all_news.extend(company_news)

                # Only continue pagination if we have a start_date and got a full page
                if not start_date or len(company_news) < limit:
                    break

                # Update end_date to the oldest date from current batch for next iteration
                current_end_date = min(news.date for news in company_news).split("T")[0]

                # If we've reached or passed the start_date, we can stop
                if current_end_date <= start_date:
                    break

            return all_news

        except Exception as e:
            print(f"Error fetching company news for {ticker}: {e}")
            return []

    async def get_insider_trades(
        self,
        ticker: str,
        end_date: str,
        start_date: Optional[str] = None,
        limit: int = 1000,
    ) -> List[InsiderTrade]:
        """Fetch insider trades for a ticker within date range."""
        try:
            all_trades = []
            current_end_date = end_date

            while True:
                url = f"{self.base_url}/insider-trades/?ticker={ticker}&filing_date_lte={current_end_date}"
                if start_date:
                    url += f"&filing_date_gte={start_date}"
                url += f"&limit={limit}"

                response = requests.get(url, headers=self._get_headers(), timeout=self.timeout)

                if response.status_code != 200:
                    break

                trades_response = InsiderTradeResponse(**response.json())
                insider_trades = trades_response.insider_trades or []

                if not insider_trades:
                    break

                all_trades.extend(insider_trades)

                # Only continue pagination if we have a start_date and got a full page
                if not start_date or len(insider_trades) < limit:
                    break

                # Update end_date to the oldest filing date from current batch for next iteration
                current_end_date = min(trade.filing_date for trade in insider_trades).split("T")[0]

                # If we've reached or passed the start_date, we can stop
                if current_end_date <= start_date:
                    break

            return all_trades

        except Exception as e:
            print(f"Error fetching insider trades for {ticker}: {e}")
            return []

    @property
    def provider_name(self) -> str:
        """Get the provider name."""
        return "financial_datasets"
