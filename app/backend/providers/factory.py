"""Factory for creating financial data providers."""

import os
from typing import Dict, Type
from .financial_base import FinancialDataProvider
from .financial_datasets import FinancialDatasetsProvider
from .yahoo_finance import YahooFinanceProvider


# Registry of available providers
PROVIDER_REGISTRY: Dict[str, Type[FinancialDataProvider]] = {
    "financial_datasets": FinancialDatasetsProvider,
    "yahoo_finance": YahooFinanceProvider,
}

# Default provider
DEFAULT_PROVIDER = "financial_datasets"


def get_financial_provider(provider_name: str = None) -> FinancialDataProvider:
    """Get the configured financial data provider.

    Args:
        provider_name: Optional provider name. If None, uses environment configuration.

    Returns:
        Configured financial data provider instance

    Raises:
        ValueError: If the configured provider is not available
        ImportError: If required dependencies for the provider are missing
    """
    # Get provider name from parameter or environment variable (support both old and new names)
    if provider_name is None:
        provider_name = (
            os.environ.get("FINANCIAL_DATA_PROVIDER") or
            os.environ.get("STOCK_DATA_PROVIDER") or
            DEFAULT_PROVIDER
        )

    provider_name = provider_name.lower()

    # Get provider class
    provider_class = PROVIDER_REGISTRY.get(provider_name)
    if not provider_class:
        available_providers = ", ".join(PROVIDER_REGISTRY.keys())
        raise ValueError(
            f"Unknown financial data provider: {provider_name}. "
            f"Available providers: {available_providers}"
        )

    # Get provider-specific timeout
    timeout_env_var = f"{provider_name.upper()}_TIMEOUT"
    timeout = int(os.environ.get(timeout_env_var, "30"))

    # Create and return provider instance
    try:
        return provider_class(timeout=timeout)
    except ImportError as e:
        raise ImportError(
            f"Failed to initialize {provider_name} provider: {e}. "
            f"Make sure all required dependencies are installed."
        ) from e


def get_stock_provider() -> FinancialDataProvider:
    """Get the configured stock data provider (backward compatibility).

    Returns:
        Configured financial data provider instance

    Raises:
        ValueError: If the configured provider is not available
        ImportError: If required dependencies for the provider are missing
    """
    return get_financial_provider()


def get_available_providers() -> Dict[str, str]:
    """Get a dictionary of available providers and their descriptions.
    
    Returns:
        Dictionary mapping provider names to descriptions
    """
    return {
        "financial_datasets": "Financial Datasets API - Professional financial data service",
        "yahoo_finance": "Yahoo Finance - Free financial data via yfinance library",
    }


def is_provider_available(provider_name: str) -> bool:
    """Check if a provider is available and can be initialized.
    
    Args:
        provider_name: Name of the provider to check
        
    Returns:
        True if provider is available, False otherwise
    """
    provider_class = PROVIDER_REGISTRY.get(provider_name.lower())
    if not provider_class:
        return False
    
    try:
        # Try to create an instance to check for missing dependencies
        provider_class(timeout=30)
        return True
    except (ImportError, Exception):
        return False
