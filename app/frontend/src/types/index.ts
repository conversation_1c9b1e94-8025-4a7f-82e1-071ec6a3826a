// Types for the FinancialAI Frontend Application

export interface Stock {
  ticker: string;
  name: string;
  exchange?: string;
  sector?: string;
  industry?: string;
}

export interface Analyst {
  id: string;
  displayName: string;
  description?: string;
}

export interface LLMProvider {
  id: string;
  name: string;
  description?: string;
}

export interface LLMModel {
  id: string;
  name: string;
  provider: string;
  description?: string;
}

export interface DataProvider {
  id: string;
  name: string;
  description?: string;
}

export interface NewsItem {
  ticker: string;
  title: string;
  author: string;
  source: string;
  date: string;
  url: string;
  sentiment?: string;
}

export interface NewsResponse {
  success: boolean;
  data: NewsItem[];
  meta: {
    ticker: string;
    start_date: string;
    end_date: string;
    limit: number;
    count: number;
  };
}

export interface AnalysisRequest {
  tickers: string[];
  selectedAnalysts: string[];
  modelProvider: string;
  modelName: string;
  startDate?: string;
  endDate?: string;
  initialCash?: number;
  marginRequirement?: number;
}

export interface AnalysisProgress {
  agentName: string;
  ticker: string | null;
  status: string;
  timestamp: string;
}

export interface AnalystSignal {
  signal: 'bullish' | 'bearish' | 'neutral';
  confidence: number;
  reasoning: string;
}

export interface TradingDecision {
  action: 'buy' | 'sell' | 'short' | 'cover' | 'hold';
  quantity: number;
  confidence: number;
  reasoning: string;
}

export interface AnalysisResult {
  decisions: Record<string, TradingDecision>;
  analystSignals: Record<string, Record<string, AnalystSignal>>;
}

export interface AnalysisState {
  isRunning: boolean;
  progress: AnalysisProgress[];
  result: AnalysisResult | null;
  error: string | null;
  cancelFunction?: () => void;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface CompanyFacts {
  ticker: string;
  name: string;
  cik?: string;
  industry?: string;
  sector?: string;
  category?: string;
  exchange?: string;
  isActive?: boolean;
  listingDate?: string;
  location?: string;
  marketCap?: number;
  numberOfEmployees?: number;
  secFilingsUrl?: string;
  sicCode?: string;
  sicIndustry?: string;
  sicSector?: string;
  websiteUrl?: string;
  weightedAverageShares?: number;
}

// Form validation types
export interface FormErrors {
  stocks?: string;
  analysts?: string;
  provider?: string;
  model?: string;
}

export interface FormData {
  selectedStocks: Stock[];
  selectedAnalysts: string[];
  selectedProvider: string;
  selectedModel: string;
  startDate: string;
  endDate: string;
  initialCash: number;
  marginRequirement: number;
}

// Backtest-specific types
export interface BacktestRequest {
  tickers: string[];
  selectedAnalysts: string[];
  modelProvider: string;
  modelName: string;
  startDate: string;
  endDate: string;
  initialCapital: number;
  marginRequirement: number;
}

export interface BacktestProgress {
  message: string;
  step: string;
}

export interface TradingData {
  ticker: string;
  action: string;
  quantity: number;
  price: number;
  shares_owned: number;
  position_value: number;
  bullish_count: number;
  bearish_count: number;
  neutral_count: number;
}

export interface PortfolioSummary {
  cash_balance: number;
  total_position_value: number;
  total_value: number;
  return_pct: number;
  sharpe_ratio?: number;
  sortino_ratio?: number;
  max_drawdown?: number;
}

export interface DetailedProgress {
  type: string;
  date: string;
  portfolio_summary: PortfolioSummary;
  trading_data: TradingData[];
}

export interface BacktestResults {
  totalReturn?: number;
  sharpeRatio?: number;
  maxDrawdown?: number;
  sortinoRatio?: number;
  finalPortfolioValue?: number;
}

export interface BacktestSummary {
  startDate: string;
  endDate: string;
  initialCapital: number;
  tickers: string[];
  analystsUsed: string[];
  modelUsed: string;
}

export interface BacktestResult {
  results: BacktestResults;
  performanceMetrics: Record<string, any>;
  backtestSummary: BacktestSummary;
  chartData?: string;
}

export interface BacktestState {
  isRunning: boolean;
  progress: BacktestProgress[];
  detailedProgress: DetailedProgress[];
  result: BacktestResult | null;
  error: string | null;
  cancelFunction?: () => void;
}

export interface BacktestFormData {
  selectedStocks: Stock[];
  selectedAnalysts: string[];
  selectedProvider: string;
  selectedModel: string;
  startDate: string;
  endDate: string;
  initialCapital: number;
  marginRequirement: number;
}

export interface BacktestFormErrors {
  stocks?: string;
  analysts?: string;
  provider?: string;
  model?: string;
  startDate?: string;
  endDate?: string;
  initialCapital?: string;
  marginRequirement?: string;
}
