import axios from 'axios';
import {
  Stock,
  Analyst,
  LLMProvider,
  LLMModel,
  AnalysisRequest,
  AnalysisResult,
  CompanyFacts,
  ApiResponse,
  DataProvider,
  NewsItem,
  NewsResponse,
  BacktestRequest,
  BacktestResult
} from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

// Stock-related API calls
export const searchStocks = async (query: string): Promise<Stock[]> => {
  try {
    const response = await api.get(`/stocks/search?q=${encodeURIComponent(query)}`);
    return response.data.data || [];
  } catch (error) {
    console.error('Error searching stocks:', error);
    return [];
  }
};

export const getCompanyFacts = async (ticker: string): Promise<CompanyFacts | null> => {
  try {
    const response = await api.get(`/stocks/${ticker}/facts`);
    return response.data.data || null;
  } catch (error) {
    console.error('Error fetching company facts:', error);
    return null;
  }
};

export const validateTickers = async (tickers: string[]): Promise<{ valid: string[], invalid: string[] }> => {
  try {
    const response = await api.post('/stocks/validate', { tickers });
    return response.data.data || { valid: [], invalid: tickers };
  } catch (error) {
    console.error('Error validating tickers:', error);
    return { valid: [], invalid: tickers };
  }
};

// Data provider API calls
export const getDataProviders = async (): Promise<DataProvider[]> => {
  try {
    const response = await api.get('/stocks/providers');
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching data providers:', error);
    return [];
  }
};

// News API calls
export const getCompanyNews = async (
  ticker: string,
  startDate?: string,
  endDate?: string,
  limit: number = 50,
  provider?: string
): Promise<NewsItem[]> => {
  try {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    params.append('limit', limit.toString());
    if (provider) params.append('provider', provider);

    const response = await api.get(`/stocks/${ticker}/news?${params.toString()}`);
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching company news:', error);
    return [];
  }
};

// Analyst-related API calls
export const getAvailableAnalysts = async (): Promise<Analyst[]> => {
  try {
    const response = await api.get('/analysts');
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching analysts:', error);
    return [];
  }
};

// LLM Provider and Model API calls
export const getAvailableProviders = async (): Promise<LLMProvider[]> => {
  try {
    const response = await api.get('/llm/providers');
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching LLM providers:', error);
    return [];
  }
};

export const getAvailableModels = async (provider: string): Promise<LLMModel[]> => {
  try {
    const response = await api.get(`/llm/providers/${provider}/models`);
    return response.data.data || [];
  } catch (error) {
    console.error('Error fetching models for provider:', error);
    return [];
  }
};

export const checkOllamaStatus = async (): Promise<{ running: boolean, models: string[] }> => {
  try {
    const response = await api.get('/llm/ollama/status');
    return response.data.data || { running: false, models: [] };
  } catch (error) {
    console.error('Error checking Ollama status:', error);
    return { running: false, models: [] };
  }
};

export const checkLMStudioStatus = async (): Promise<{ running: boolean, models: string[] }> => {
  try {
    const response = await api.get('/llm/lmstudio/status');
    return response.data.data || { running: false, models: [] };
  } catch (error) {
    console.error('Error checking LM Studio status:', error);
    return { running: false, models: [] };
  }
};

// Analysis API calls
export const runAnalysis = (
  request: AnalysisRequest,
  onProgress: (progress: any) => void,
  onComplete: (result: AnalysisResult) => void,
  onError: (error: string) => void
): () => void => {
  const controller = new AbortController();
  
  const runStream = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/hedge-fund/run`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tickers: request.tickers,
          selected_agents: request.selectedAnalysts,
          model_name: request.modelName,
          model_provider: request.modelProvider,
          start_date: request.startDate,
          end_date: request.endDate,
          initial_cash: request.initialCash || 100000,
          margin_requirement: request.marginRequirement || 0,
        }),
        signal: controller.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;
          
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === 'progress') {
                onProgress(data);
              } else if (data.type === 'complete') {
                // Transform the API response to match our frontend types
                const transformedResult = {
                  decisions: data.data?.decisions || {},
                  analystSignals: data.data?.analyst_signals || {},
                };
                console.log('Received analysis result:', data.data);
                console.log('Transformed result:', transformedResult);
                onComplete(transformedResult);
                return;
              } else if (data.type === 'error') {
                onError(data.message || 'Analysis failed');
                return;
              }
            } catch (parseError) {
              console.error('Error parsing SSE data:', parseError);
            }
          }
        }
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('Analysis cancelled');
      } else {
        console.error('Analysis error:', error);
        onError(error.message || 'Analysis failed');
      }
    }
  };

  runStream();

  // Return cancel function
  return () => {
    controller.abort();
  };
};

// Backtest API calls
export const runBacktest = (
  request: BacktestRequest,
  onProgress: (progress: any) => void,
  onComplete: (result: BacktestResult) => void,
  onError: (error: string) => void
): () => void => {
  const controller = new AbortController();

  const runStream = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/backtest/run`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tickers: request.tickers,
          selected_analysts: request.selectedAnalysts,
          model_name: request.modelName,
          model_provider: request.modelProvider,
          start_date: request.startDate,
          end_date: request.endDate,
          initial_capital: request.initialCapital,
          margin_requirement: request.marginRequirement,
        }),
        signal: controller.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('Failed to get response reader');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;

          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));

              if (data.type === 'progress') {
                onProgress(data.data);
              } else if (data.type === 'detailed_progress') {
                onProgress(data.data);
              } else if (data.type === 'complete') {
                console.log('Received backtest result:', data.data);
                onComplete(data.data);
                return;
              } else if (data.type === 'error') {
                onError(data.data?.message || 'Backtest failed');
                return;
              }
            } catch (parseError) {
              console.error('Error parsing SSE data:', parseError);
            }
          }
        }
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('Backtest cancelled');
      } else {
        console.error('Backtest error:', error);
        onError(error.message || 'Backtest failed');
      }
    }
  };

  runStream();

  // Return cancel function
  return () => {
    controller.abort();
  };
};

export const getBacktestStatus = async (): Promise<{ available: boolean, message: string }> => {
  try {
    const response = await api.get('/backtest/status');
    return response.data.data || { available: false, message: 'Backtest not available' };
  } catch (error) {
    console.error('Error checking backtest status:', error);
    return { available: false, message: 'Error checking backtest status' };
  }
};

export default api;
