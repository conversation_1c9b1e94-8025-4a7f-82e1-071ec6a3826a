import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Button,
  TextField,
  Alert,
} from '@mui/material';
import { Search, Refresh } from '@mui/icons-material';
import StockSelector from '../components/StockSelector';
import DataProviderSelector from '../components/DataProviderSelector';
import NewsGrid from '../components/NewsGrid';
import {
  Stock,
  DataProvider,
  NewsItem,
} from '../types';
import {
  getDataProviders,
  getCompanyNews,
} from '../services/api';

interface NewsFormData {
  selectedStock: Stock | null;
  selectedProvider: string;
  startDate: string;
  endDate: string;
  limit: number;
}

interface NewsFormErrors {
  stock?: string;
  provider?: string;
  dateRange?: string;
}

const NewsPage: React.FC = () => {
  const [formData, setFormData] = useState<NewsFormData>({
    selectedStock: null,
    selectedProvider: '',
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    endDate: new Date().toISOString().split('T')[0], // today
    limit: 50,
  });

  const [formErrors, setFormErrors] = useState<NewsFormErrors>({});
  const [providers, setProviders] = useState<DataProvider[]>([]);
  const [news, setNews] = useState<NewsItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Load initial data
  useEffect(() => {
    const loadProviders = async () => {
      try {
        const providersData = await getDataProviders();
        setProviders(providersData);

        // Auto-select first provider if available
        if (providersData.length > 0 && !formData.selectedProvider) {
          setFormData(prev => ({ ...prev, selectedProvider: providersData[0].id }));
        }
      } catch (error) {
        console.error('Error loading data providers:', error);
        setError('Failed to load data providers');
      }
    };

    loadProviders();
  }, []);

  const validateForm = (): boolean => {
    const errors: NewsFormErrors = {};

    if (!formData.selectedStock) {
      errors.stock = 'Please select a stock';
    }

    if (!formData.selectedProvider) {
      errors.provider = 'Please select a data provider';
    }

    if (new Date(formData.startDate) > new Date(formData.endDate)) {
      errors.dateRange = 'Start date must be before end date';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleFetchNews = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError('');
    setNews([]);

    try {
      const newsData = await getCompanyNews(
        formData.selectedStock!.ticker,
        formData.startDate,
        formData.endDate,
        formData.limit,
        formData.selectedProvider
      );

      setNews(newsData);
    } catch (error) {
      console.error('Error fetching news:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch news');
    } finally {
      setLoading(false);
    }
  };

  const handleStockChange = (stocks: Stock[]) => {
    // Only allow single selection for news
    const selectedStock = stocks.length > 0 ? stocks[0] : null;
    setFormData(prev => ({ ...prev, selectedStock }));
    
    // Clear previous news when stock changes
    if (selectedStock?.ticker !== formData.selectedStock?.ticker) {
      setNews([]);
      setError('');
    }
  };

  const handleRefresh = () => {
    if (formData.selectedStock) {
      handleFetchNews();
    }
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom sx={{ mb: 4 }}>
        Company News
      </Typography>

      <Grid container spacing={3}>
        {/* Configuration Panel */}
        <Grid size={{ xs: 12, md: 4 }}>
          <Paper elevation={3} sx={{ p: 3, height: 'fit-content' }}>
            <Typography variant="h5" gutterBottom>
              News Configuration
            </Typography>

            <Box sx={{ mt: 3 }}>
              <StockSelector
                selectedStocks={formData.selectedStock ? [formData.selectedStock] : []}
                onChange={handleStockChange}
                error={formErrors.stock}
                maxSelections={1}
              />
            </Box>

            <Box sx={{ mt: 3 }}>
              <DataProviderSelector
                providers={providers}
                selectedProvider={formData.selectedProvider}
                onChange={(provider) => setFormData(prev => ({ ...prev, selectedProvider: provider }))}
                error={formErrors.provider}
              />
            </Box>

            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Date Range
              </Typography>
              <Grid container spacing={2}>
                <Grid size={{ xs: 6 }}>
                  <TextField
                    fullWidth
                    label="Start Date"
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                    InputLabelProps={{ shrink: true }}
                    size="small"
                  />
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <TextField
                    fullWidth
                    label="End Date"
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                    InputLabelProps={{ shrink: true }}
                    size="small"
                  />
                </Grid>
              </Grid>
              {formErrors.dateRange && (
                <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
                  {formErrors.dateRange}
                </Typography>
              )}
            </Box>

            <Box sx={{ mt: 3 }}>
              <TextField
                fullWidth
                label="Number of Articles"
                type="number"
                value={formData.limit}
                onChange={(e) => setFormData(prev => ({ ...prev, limit: parseInt(e.target.value) || 50 }))}
                inputProps={{ min: 1, max: 200 }}
                size="small"
              />
            </Box>

            <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                startIcon={<Search />}
                onClick={handleFetchNews}
                disabled={loading || !formData.selectedStock || !formData.selectedProvider}
                fullWidth
              >
                {loading ? 'Loading...' : 'Fetch News'}
              </Button>
              
              {news.length > 0 && (
                <Button
                  variant="outlined"
                  startIcon={<Refresh />}
                  onClick={handleRefresh}
                  disabled={loading}
                >
                  Refresh
                </Button>
              )}
            </Box>

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* News Display Panel */}
        <Grid size={{ xs: 12, md: 8 }}>
          <NewsGrid
            news={news}
            loading={loading}
            error={error}
            ticker={formData.selectedStock?.ticker}
          />
        </Grid>
      </Grid>
    </Container>
  );
};

export default NewsPage;
