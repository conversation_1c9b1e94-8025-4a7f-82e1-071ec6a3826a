import React, { useState } from 'react';
import {
  I<PERSON><PERSON><PERSON>on,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Box,
  Divider,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Assessment,
  Article,
  Close,
  TrendingUp,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';

interface HamburgerMenuProps {
  title?: string;
}

const HamburgerMenu: React.FC<HamburgerMenuProps> = ({ title = "FinancialAI" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      text: 'Analysis',
      icon: <Assessment />,
      path: '/',
      description: 'Run AI-powered stock analysis'
    },
    {
      text: 'Backtest',
      icon: <TrendingUp />,
      path: '/backtest',
      description: 'Run historical backtesting'
    },
    {
      text: 'News',
      icon: <Article />,
      path: '/news',
      description: 'View company news and updates'
    },
  ];

  const handleMenuClick = (path: string) => {
    navigate(path);
    setIsOpen(false);
  };

  const toggleDrawer = (open: boolean) => {
    setIsOpen(open);
  };

  return (
    <>
      <IconButton
        edge="start"
        color="inherit"
        aria-label="menu"
        onClick={() => toggleDrawer(true)}
        sx={{ mr: 2 }}
      >
        <MenuIcon />
      </IconButton>

      <Drawer
        anchor="left"
        open={isOpen}
        onClose={() => toggleDrawer(false)}
        PaperProps={{
          sx: { width: 280 }
        }}
      >
        <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
            {title}
          </Typography>
          <IconButton onClick={() => toggleDrawer(false)}>
            <Close />
          </IconButton>
        </Box>
        
        <Divider />
        
        <List>
          {menuItems.map((item) => (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                onClick={() => handleMenuClick(item.path)}
                selected={location.pathname === item.path}
                sx={{
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    color: 'primary.contrastText',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                    '& .MuiListItemIcon-root': {
                      color: 'primary.contrastText',
                    },
                  },
                }}
              >
                <ListItemIcon>
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  secondary={item.description}
                  secondaryTypographyProps={{
                    sx: {
                      color: location.pathname === item.path ? 'primary.contrastText' : 'text.secondary',
                      opacity: location.pathname === item.path ? 0.8 : 1,
                    }
                  }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Drawer>
    </>
  );
};

export default HamburgerMenu;
