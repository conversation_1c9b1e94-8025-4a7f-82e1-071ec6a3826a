import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardA<PERSON>,
  Typography,
  Button,
  Box,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Link,
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess,
  OpenInNew,
  Close,
  TrendingUp,
  TrendingDown,
  TrendingFlat,
} from '@mui/icons-material';
import { NewsItem } from '../types';

interface NewsCardProps {
  news: NewsItem;
}

const NewsCard: React.FC<NewsCardProps> = ({ news }) => {
  const [expanded, setExpanded] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  const getSentimentIcon = (sentiment?: string) => {
    if (!sentiment) return undefined;

    switch (sentiment.toLowerCase()) {
      case 'positive':
        return <TrendingUp fontSize="small" color="success" />;
      case 'negative':
        return <TrendingDown fontSize="small" color="error" />;
      case 'neutral':
        return <TrendingFlat fontSize="small" color="action" />;
      default:
        return undefined;
    }
  };

  const getSentimentColor = (sentiment?: string) => {
    if (!sentiment) return 'default';
    
    switch (sentiment.toLowerCase()) {
      case 'positive':
        return 'success';
      case 'negative':
        return 'error';
      case 'neutral':
        return 'default';
      default:
        return 'default';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return dateString;
    }
  };

  const truncateTitle = (title: string, maxLength: number = 100) => {
    if (title.length <= maxLength) return title;
    return title.substring(0, maxLength) + '...';
  };

  return (
    <>
      <Card 
        sx={{ 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column',
          transition: 'transform 0.2s, box-shadow 0.2s',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: 4,
          }
        }}
      >
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography variant="caption" color="text.secondary">
              {news.source}
            </Typography>
            {news.sentiment && (
              <Chip
                icon={getSentimentIcon(news.sentiment)}
                label={news.sentiment}
                size="small"
                color={getSentimentColor(news.sentiment) as any}
                variant="outlined"
              />
            )}
          </Box>

          <Typography 
            variant="h6" 
            component="h3" 
            gutterBottom
            sx={{ 
              fontSize: '1rem',
              lineHeight: 1.3,
              fontWeight: 'medium'
            }}
          >
            {expanded ? news.title : truncateTitle(news.title)}
          </Typography>

          <Typography variant="body2" color="text.secondary" gutterBottom>
            By {news.author} • {formatDate(news.date)}
          </Typography>

          <Typography variant="body2" color="text.secondary">
            {news.ticker}
          </Typography>
        </CardContent>

        <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
          <Button
            size="small"
            onClick={() => setExpanded(!expanded)}
            startIcon={expanded ? <ExpandLess /> : <ExpandMore />}
          >
            {expanded ? 'Show Less' : 'Show More'}
          </Button>
          
          <Button
            size="small"
            onClick={() => setDialogOpen(true)}
            variant="outlined"
          >
            Read Full
          </Button>
        </CardActions>
      </Card>

      {/* Full Article Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { maxHeight: '80vh' }
        }}
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h6" component="div">
              {news.title}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {news.source} • By {news.author} • {formatDate(news.date)}
            </Typography>
          </Box>
          <IconButton onClick={() => setDialogOpen(false)}>
            <Close />
          </IconButton>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mb: 2 }}>
            {news.sentiment && (
              <Chip
                icon={getSentimentIcon(news.sentiment)}
                label={`Sentiment: ${news.sentiment}`}
                color={getSentimentColor(news.sentiment) as any}
                variant="outlined"
                sx={{ mr: 1 }}
              />
            )}
            <Chip
              label={`Ticker: ${news.ticker}`}
              variant="outlined"
            />
          </Box>

          <Typography variant="body1" paragraph>
            {news.title}
          </Typography>

          <Typography variant="body2" color="text.secondary">
            This is a preview of the news article. Click the link below to read the full article on the original source.
          </Typography>
        </DialogContent>

        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Close
          </Button>
          <Button
            variant="contained"
            startIcon={<OpenInNew />}
            component={Link}
            href={news.url}
            target="_blank"
            rel="noopener noreferrer"
          >
            Read Original
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default NewsCard;
