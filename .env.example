# For running LLMs hosted by anthropic (claude-3-5-sonnet, claude-3-opus, claude-3-5-haiku)
# Get your Anthropic API key from https://anthropic.com/
ANTHROPIC_API_KEY=your-anthropic-api-key

# For running LLMs hosted by deepseek (deepseek-chat, deepseek-reasoner, etc.)
# Get your DeepSeek API key from https://deepseek.com/
DEEPSEEK_API_KEY=your-deepseek-api-key

# For running LLMs hosted by groq (deepseek, llama3, etc.)
# Get your Groq API key from https://groq.com/
GROQ_API_KEY=your-groq-api-key

# For running LLMs hosted by gemini (gemini-2.5-flash, gemini-2.5-pro)
# Get your Google API key from https://ai.dev/
GOOGLE_API_KEY=your-google-api-key

# For getting financial data to power the hedge fund
# Get your Financial Datasets API key from https://financialdatasets.ai/
FINANCIAL_DATASETS_API_KEY=your-financial-datasets-api-key

# Financial data provider configuration
# Options: "financial_datasets" (default), "yahoo_finance"
# This affects all financial data: stocks, prices, metrics, news, insider trades, etc.
FINANCIAL_DATA_PROVIDER=financial_datasets

# Provider-specific timeouts (in seconds)
FINANCIAL_DATASETS_TIMEOUT=30
YAHOO_FINANCE_TIMEOUT=30

# For running LLMs hosted by openai (gpt-4o, gpt-4o-mini, etc.)
# Get your OpenAI API key from https://platform.openai.com/
OPENAI_API_KEY=

# For running LLMs hosted by openrouter (claude, gpt, llama, etc.)
# Get your Openrouter API key from https://openrouter.ai/
OPENROUTER_API_KEY=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
# For running LLMs hosted by LM Studio (local inference)
# Configure the base URL for your LM Studio server (default: http://localhost:1234/v1)
LMSTUDIO_BASE_URL=http://localhost:1234/v1

# Timeout settings for LLM providers (in seconds, default: 36000 = 10 hours)
# Adjust these values based on your needs and model response times
OPENAI_TIMEOUT=36000
OPENROUTER_TIMEOUT=36000
ANTHROPIC_TIMEOUT=36000
GROQ_TIMEOUT=36000
DEEPSEEK_TIMEOUT=36000
GOOGLE_TIMEOUT=36000
OLLAMA_TIMEOUT=36000
LMSTUDIO_TIMEOUT=36000
