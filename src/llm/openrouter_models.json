[{"display_name": "[openrouter] claude 3.5 sonnet", "model_name": "anthropic/claude-3.5-sonnet", "provider": "Openrouter"}, {"display_name": "[openrouter] claude 3.5 haiku", "model_name": "anthropic/claude-3.5-haiku", "provider": "Openrouter"}, {"display_name": "[openrouter] gpt 4o", "model_name": "openai/gpt-4o", "provider": "Openrouter"}, {"display_name": "[openrouter] gpt 4o mini", "model_name": "openai/gpt-4o-mini", "provider": "Openrouter"}, {"display_name": "[openrouter] o1 preview", "model_name": "openai/o1-preview", "provider": "Openrouter"}, {"display_name": "[openrouter] o1 mini", "model_name": "openai/o1-mini", "provider": "Openrouter"}, {"display_name": "[openrouter] llama 3.1 405b instruct", "model_name": "meta-llama/llama-3.1-405b-instruct", "provider": "Openrouter"}, {"display_name": "[openrouter] llama 3.1 70b instruct", "model_name": "meta-llama/llama-3.1-70b-instruct", "provider": "Openrouter"}, {"display_name": "[openrouter] llama 3.1 8b instruct", "model_name": "meta-llama/llama-3.1-8b-instruct", "provider": "Openrouter"}, {"display_name": "[openrouter] gemini pro 1.5", "model_name": "google/gemini-pro-1.5", "provider": "Openrouter"}, {"display_name": "[openrouter] gemini flash 1.5", "model_name": "google/gemini-flash-1.5", "provider": "Openrouter"}, {"display_name": "[openrouter] deepseek r1", "model_name": "deepseek/deepseek-r1", "provider": "Openrouter"}, {"display_name": "[openrouter] deepseek v3", "model_name": "deepseek/deepseek-chat", "provider": "Openrouter"}, {"display_name": "[openrouter] qwen 2.5 72b instruct", "model_name": "qwen/qwen-2.5-72b-instruct", "provider": "Openrouter"}, {"display_name": "[openrouter] mistral large", "model_name": "mistralai/mistral-large", "provider": "Openrouter"}, {"display_name": "[openrouter] mixtral 8x7b instruct", "model_name": "mistralai/mixtral-8x7b-instruct", "provider": "Openrouter"}, {"display_name": "[openrouter] custom", "model_name": "-", "provider": "Openrouter"}]