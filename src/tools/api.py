"""Financial data API tools using configurable providers.

This module provides financial data access through configurable providers.
It maintains backward compatibility with the original API while supporting
multiple data sources (Financial Datasets, Yahoo Finance, etc.).
"""

import asyncio
import pandas as pd
from typing import List, Optional
from src.data.cache import get_cache
from src.data.models import (
    CompanyNews,
    FinancialMetrics,
    Price,
    LineItem,
    InsiderTrade,
)
from app.backend.providers import get_financial_provider

# Global cache instance
_cache = get_cache()

# Global provider instance
_provider = None


def get_provider():
    """Get the global provider instance."""
    global _provider
    if _provider is None:
        _provider = get_financial_provider()
    return _provider


def _run_async_safely(coro):
    """Run an async coroutine safely, handling both sync and async contexts."""
    try:
        # Try to get the current event loop
        loop = asyncio.get_running_loop()
        # If we're in an async context, we need to run in a thread
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(asyncio.run, coro)
            return future.result()
    except RuntimeError:
        # No event loop running, safe to use asyncio.run
        return asyncio.run(coro)


def get_prices(ticker: str, start_date: str, end_date: str) -> List[Price]:
    """Fetch price data from cache or provider."""
    # Create a cache key that includes all parameters to ensure exact matches
    cache_key = f"{ticker}_{start_date}_{end_date}"

    # Check cache first - simple exact match
    if cached_data := _cache.get_prices(cache_key):
        return [Price(**price) for price in cached_data]

    # If not in cache, fetch from provider
    try:
        provider = get_provider()
        prices = _run_async_safely(provider.get_prices(ticker, start_date, end_date))

        if prices:
            # Cache the results using the comprehensive cache key
            _cache.set_prices(cache_key, [p.model_dump() for p in prices])

        return prices
    except Exception as e:
        print(f"Error fetching prices for {ticker}: {e}")
        return []


def get_financial_metrics(
    ticker: str,
    end_date: str,
    period: str = "ttm",
    limit: int = 10,
) -> List[FinancialMetrics]:
    """Fetch financial metrics from cache or provider."""
    # Create a cache key that includes all parameters to ensure exact matches
    cache_key = f"{ticker}_{period}_{end_date}_{limit}"

    # Check cache first - simple exact match
    if cached_data := _cache.get_financial_metrics(cache_key):
        return [FinancialMetrics(**metric) for metric in cached_data]

    # If not in cache, fetch from provider
    try:
        provider = get_provider()
        financial_metrics = _run_async_safely(
            provider.get_financial_metrics(ticker, end_date, period, limit)
        )

        if financial_metrics:
            # Cache the results as dicts using the comprehensive cache key
            _cache.set_financial_metrics(cache_key, [m.model_dump() for m in financial_metrics])

        return financial_metrics
    except Exception as e:
        print(f"Error fetching financial metrics for {ticker}: {e}")
        return []


def search_line_items(
    ticker: str,
    line_items: List[str],
    end_date: str,
    period: str = "ttm",
    limit: int = 10,
) -> List[LineItem]:
    """Fetch line items from provider."""
    try:
        provider = get_provider()
        return _run_async_safely(
            provider.search_line_items(ticker, line_items, end_date, period, limit)
        )
    except Exception as e:
        print(f"Error searching line items for {ticker}: {e}")
        return []


def get_insider_trades(
    ticker: str,
    end_date: str,
    start_date: Optional[str] = None,
    limit: int = 1000,
) -> List[InsiderTrade]:
    """Fetch insider trades from cache or provider."""
    # Create a cache key that includes all parameters to ensure exact matches
    cache_key = f"{ticker}_{start_date or 'none'}_{end_date}_{limit}"

    # Check cache first - simple exact match
    if cached_data := _cache.get_insider_trades(cache_key):
        return [InsiderTrade(**trade) for trade in cached_data]

    # If not in cache, fetch from provider
    try:
        provider = get_provider()
        all_trades = _run_async_safely(
            provider.get_insider_trades(ticker, end_date, start_date, limit)
        )

        if all_trades:
            # Cache the results using the comprehensive cache key
            _cache.set_insider_trades(cache_key, [trade.model_dump() for trade in all_trades])

        return all_trades
    except Exception as e:
        print(f"Error fetching insider trades for {ticker}: {e}")
        return []


def get_company_news(
    ticker: str,
    end_date: str,
    start_date: Optional[str] = None,
    limit: int = 1000,
) -> List[CompanyNews]:
    """Fetch company news from cache or provider."""
    # Create a cache key that includes all parameters to ensure exact matches
    cache_key = f"{ticker}_{start_date or 'none'}_{end_date}_{limit}"

    # Check cache first - simple exact match
    if cached_data := _cache.get_company_news(cache_key):
        return [CompanyNews(**news) for news in cached_data]

    # If not in cache, fetch from provider
    try:
        provider = get_provider()
        all_news = _run_async_safely(
            provider.get_company_news(ticker, end_date, start_date, limit)
        )

        if all_news:
            # Cache the results using the comprehensive cache key
            _cache.set_company_news(cache_key, [news.model_dump() for news in all_news])

        return all_news
    except Exception as e:
        print(f"Error fetching company news for {ticker}: {e}")
        return []


def get_market_cap(
    ticker: str,
    end_date: str,
) -> Optional[float]:
    """Fetch market cap from the provider."""
    try:
        provider = get_provider()
        return _run_async_safely(provider.get_market_cap(ticker, end_date))
    except Exception as e:
        print(f"Error fetching market cap for {ticker}: {e}")
        return None


def prices_to_df(prices: List[Price]) -> pd.DataFrame:
    """Convert prices to a DataFrame."""
    df = pd.DataFrame([p.model_dump() for p in prices])
    if df.empty:
        return df

    df["Date"] = pd.to_datetime(df["time"])
    df.set_index("Date", inplace=True)
    numeric_cols = ["open", "close", "high", "low", "volume"]
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors="coerce")
    df.sort_index(inplace=True)
    return df


def get_price_data(ticker: str, start_date: str, end_date: str) -> pd.DataFrame:
    """Get price data as DataFrame using provider system."""
    prices = get_prices(ticker, start_date, end_date)
    return prices_to_df(prices)
