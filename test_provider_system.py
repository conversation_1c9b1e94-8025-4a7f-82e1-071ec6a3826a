#!/usr/bin/env python3
"""
Test script to verify the provider-based financial data system.

This script tests all aspects of the new provider system:
- Provider switching via environment variables
- All financial data endpoints
- Backward compatibility
- CLI and web API integration
"""

import os
import asyncio
import datetime
from typing import List

def test_environment_config():
    """Test environment variable configuration."""
    print("=== Testing Environment Configuration ===")
    
    # Test current provider
    current_provider = os.environ.get("FINANCIAL_DATA_PROVIDER", "financial_datasets")
    print(f"Current provider: {current_provider}")
    
    # Test backward compatibility
    legacy_provider = os.environ.get("STOCK_DATA_PROVIDER")
    if legacy_provider:
        print(f"Legacy provider setting: {legacy_provider}")
    
    print()


def test_provider_switching():
    """Test switching between providers."""
    print("=== Testing Provider Switching ===")
    
    from app.backend.providers import get_financial_provider
    
    # Test default provider
    provider = get_financial_provider()
    print(f"Default provider: {provider.provider_name}")
    print(f"Provider features: {list(provider.get_provider_info()['features'].keys())}")
    
    # Test switching (temporarily)
    original_env = os.environ.get("FINANCIAL_DATA_PROVIDER")
    
    try:
        # Switch to Financial Datasets
        os.environ["FINANCIAL_DATA_PROVIDER"] = "financial_datasets"
        # Reset provider cache by creating new instance
        from app.backend.providers.factory import get_financial_provider as get_new_provider
        provider = get_new_provider()
        print(f"Switched to: {provider.provider_name}")
        
        # Switch to Yahoo Finance
        os.environ["FINANCIAL_DATA_PROVIDER"] = "yahoo_finance"
        provider = get_new_provider()
        print(f"Switched to: {provider.provider_name}")
        
    finally:
        # Restore original environment
        if original_env:
            os.environ["FINANCIAL_DATA_PROVIDER"] = original_env
        elif "FINANCIAL_DATA_PROVIDER" in os.environ:
            del os.environ["FINANCIAL_DATA_PROVIDER"]
    
    print()


async def test_financial_data_endpoints():
    """Test all financial data endpoints."""
    print("=== Testing Financial Data Endpoints ===")
    
    from app.backend.services.financial_service import get_financial_service
    
    service = get_financial_service()
    ticker = "AAPL"
    end_date = datetime.datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
    
    # Test stock search
    try:
        results = await service.search_stocks(ticker, 3)
        print(f"✓ Stock search: {len(results)} results")
    except Exception as e:
        print(f"✗ Stock search failed: {e}")
    
    # Test company facts
    try:
        facts = await service.get_company_facts(ticker)
        print(f"✓ Company facts: {facts.name if facts else 'None'}")
    except Exception as e:
        print(f"✗ Company facts failed: {e}")
    
    # Test price data
    try:
        prices = await service.get_prices(ticker, start_date, end_date)
        print(f"✓ Price data: {len(prices)} records")
    except Exception as e:
        print(f"✗ Price data failed: {e}")
    
    # Test financial metrics
    try:
        metrics = await service.get_financial_metrics(ticker, end_date)
        print(f"✓ Financial metrics: {len(metrics)} records")
    except Exception as e:
        print(f"✗ Financial metrics failed: {e}")
    
    # Test market cap
    try:
        market_cap = await service.get_market_cap(ticker, end_date)
        print(f"✓ Market cap: ${market_cap:,.0f}" if market_cap else "✓ Market cap: None")
    except Exception as e:
        print(f"✗ Market cap failed: {e}")
    
    # Test company news (may not be supported by all providers)
    try:
        news = await service.get_company_news(ticker, end_date, limit=5)
        print(f"✓ Company news: {len(news)} articles")
    except Exception as e:
        print(f"⚠ Company news: {e}")
    
    # Test insider trades (may not be supported by all providers)
    try:
        trades = await service.get_insider_trades(ticker, end_date, limit=5)
        print(f"✓ Insider trades: {len(trades)} trades")
    except Exception as e:
        print(f"⚠ Insider trades: {e}")
    
    print()


def test_backward_compatibility():
    """Test backward compatibility with existing API."""
    print("=== Testing Backward Compatibility ===")
    
    # Test that old API functions still work
    from src.tools.api import get_prices, get_financial_metrics, get_market_cap
    
    ticker = "AAPL"
    end_date = datetime.datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.datetime.now() - datetime.timedelta(days=5)).strftime('%Y-%m-%d')
    
    try:
        prices = get_prices(ticker, start_date, end_date)
        print(f"✓ Legacy get_prices: {len(prices)} records")
    except Exception as e:
        print(f"✗ Legacy get_prices failed: {e}")
    
    try:
        metrics = get_financial_metrics(ticker, end_date)
        print(f"✓ Legacy get_financial_metrics: {len(metrics)} records")
    except Exception as e:
        print(f"✗ Legacy get_financial_metrics failed: {e}")
    
    try:
        market_cap = get_market_cap(ticker, end_date)
        print(f"✓ Legacy get_market_cap: ${market_cap:,.0f}" if market_cap else "✓ Legacy get_market_cap: None")
    except Exception as e:
        print(f"✗ Legacy get_market_cap failed: {e}")
    
    print()


async def test_web_api():
    """Test web API endpoints."""
    print("=== Testing Web API ===")
    
    from app.backend.routes.stocks import search_stocks, get_stock_facts, get_provider_info
    
    try:
        result = await search_stocks(q="AAPL")
        print(f"✓ Search API: {result['success']}")
    except Exception as e:
        print(f"✗ Search API failed: {e}")
    
    try:
        result = await get_stock_facts("AAPL")
        print(f"✓ Facts API: {result['success']}")
    except Exception as e:
        print(f"✗ Facts API failed: {e}")
    
    try:
        result = await get_provider_info()
        print(f"✓ Provider info API: {result['success']}")
        print(f"  Current provider: {result['data']['name']}")
    except Exception as e:
        print(f"✗ Provider info API failed: {e}")
    
    print()


def test_cli_compatibility():
    """Test CLI component compatibility."""
    print("=== Testing CLI Compatibility ===")
    
    try:
        from src.main import run_hedge_fund
        print("✓ Main CLI module imports")
    except Exception as e:
        print(f"✗ Main CLI import failed: {e}")
    
    try:
        from src.backtester import get_price_data
        print("✓ Backtester module imports")
    except Exception as e:
        print(f"✗ Backtester import failed: {e}")
    
    try:
        from src.agents.warren_buffett import warren_buffett_agent
        print("✓ Agent modules import")
    except Exception as e:
        print(f"✗ Agent import failed: {e}")
    
    print()


async def main():
    """Run all tests."""
    print("Financial Data Provider System Test")
    print("=" * 50)
    print()
    
    test_environment_config()
    test_provider_switching()
    await test_financial_data_endpoints()
    test_backward_compatibility()
    await test_web_api()
    test_cli_compatibility()
    
    print("=" * 50)
    print("Test completed! Check for any ✗ or ⚠ symbols above.")
    print()
    print("Configuration:")
    print(f"  FINANCIAL_DATA_PROVIDER: {os.environ.get('FINANCIAL_DATA_PROVIDER', 'not set (defaults to financial_datasets)')}")
    print(f"  STOCK_DATA_PROVIDER: {os.environ.get('STOCK_DATA_PROVIDER', 'not set')}")


if __name__ == "__main__":
    asyncio.run(main())
