#!/usr/bin/env python3
"""
Test script to verify that local providers (Ollama/LM Studio) use sequential execution
while cloud providers use parallel execution.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.main import create_workflow
from app.backend.services.graph import create_graph


def test_sequential_execution_main():
    """Test sequential execution in src/main.py create_workflow function"""
    print("Testing sequential execution for local providers in src/main.py...")
    
    # Test with Ollama (should be sequential)
    selected_analysts = ["aswath_damodaran", "charlie_munger", "peter_lynch"]
    workflow_ollama = create_workflow(selected_analysts, "Ollama")
    
    # Test with LM Studio (should be sequential)  
    workflow_lmstudio = create_workflow(selected_analysts, "LMStudio")
    
    # Test with OpenAI (should be parallel)
    workflow_openai = create_workflow(selected_analysts, "OpenAI")
    
    print("✓ create_workflow function completed without errors")


def test_sequential_execution_backend():
    """Test sequential execution in app/backend/services/graph.py create_graph function"""
    print("Testing sequential execution for local providers in app/backend/services/graph.py...")
    
    # Test with Ollama (should be sequential)
    selected_agents = ["aswath_damodaran", "charlie_munger", "peter_lynch"]
    graph_ollama = create_graph(selected_agents, "Ollama")
    
    # Test with LM Studio (should be sequential)
    graph_lmstudio = create_graph(selected_agents, "LMStudio")
    
    # Test with OpenAI (should be parallel)
    graph_openai = create_graph(selected_agents, "OpenAI")
    
    print("✓ create_graph function completed without errors")


def test_single_analyst():
    """Test that single analyst works the same for all providers"""
    print("Testing single analyst execution...")
    
    selected_analysts = ["aswath_damodaran"]
    
    # Should work the same for all providers when only one analyst
    workflow_ollama = create_workflow(selected_analysts, "Ollama")
    workflow_openai = create_workflow(selected_analysts, "OpenAI")
    graph_ollama = create_graph(selected_analysts, "Ollama")
    graph_openai = create_graph(selected_analysts, "OpenAI")
    
    print("✓ Single analyst execution completed without errors")


def test_no_provider():
    """Test behavior when no provider is specified"""
    print("Testing execution with no provider specified...")
    
    selected_analysts = ["aswath_damodaran", "charlie_munger"]
    
    # Should default to parallel execution
    workflow_none = create_workflow(selected_analysts, None)
    graph_none = create_graph(selected_analysts, None)
    
    print("✓ No provider execution completed without errors")


if __name__ == "__main__":
    print("Testing sequential vs parallel execution for local vs cloud providers...\n")
    
    try:
        test_sequential_execution_main()
        print()
        
        test_sequential_execution_backend()
        print()
        
        test_single_analyst()
        print()
        
        test_no_provider()
        print()
        
        print("🎉 All tests passed! Sequential execution is properly implemented.")
        print("\nBehavior Summary:")
        print("- Ollama/LM Studio with multiple analysts: Sequential execution")
        print("- Cloud providers with multiple analysts: Parallel execution")
        print("- Single analyst: Same behavior for all providers")
        print("- No provider specified: Defaults to parallel execution")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        sys.exit(1)
